import { useMemo, useRef } from "react";
import { useReducedMotion } from "framer-motion";
import { slideVariants, fadeVariants } from "@/utils/animationVariants";
import type { TabAnimationConfig, TabKey } from "@/types/animations";

/**
 * 智能 Tab 动画管理 Hook
 * 自动检测切换方向并选择合适的动画变体
 */
export const useTabAnimations = (selectedTab: string): TabAnimationConfig => {
  const previousTab = useRef<string>(selectedTab);
  const shouldReduceMotion = useReducedMotion();
  
  // 智能方向检测算法
  const direction = useMemo(() => {
    const tabOrder: Record<string, number> = { 
      home: 0, 
      host: 1 
    };
    
    const current = tabOrder[selectedTab] ?? 0;
    const previous = tabOrder[previousTab.current] ?? 0;
    
    // 更新上一个 tab 引用
    previousTab.current = selectedTab;
    
    // 返回方向：1 为向右，-1 为向左
    return current > previous ? 1 : -1;
  }, [selectedTab]);

  // 根据用户偏好选择动画变体
  const variants = shouldReduceMotion ? fadeVariants : slideVariants;
  
  return {
    direction,
    variants,
    shouldReduceMotion
  };
};

/**
 * 获取 Tab 切换方向的工具函数
 */
export const getSlideDirection = (fromTab: TabKey, toTab: TabKey): number => {
  const tabOrder: Record<TabKey, number> = { 
    home: 0, 
    host: 1 
  };
  
  return tabOrder[toTab] > tabOrder[fromTab] ? 1 : -1;
};
