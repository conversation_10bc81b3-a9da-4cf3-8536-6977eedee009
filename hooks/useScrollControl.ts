/**
 * iPad滚动控制Hook
 * 提供JavaScript增强的滚动控制功能
 */

import { useEffect, useCallback, useState } from 'react';

interface ScrollControlConfig {
  isIPad: boolean;
  isScrollDisabled: boolean;
  enableScrollControl: () => void;
  disableScrollControl: () => void;
}

/**
 * 检测是否为iPad设备
 */
const detectIPad = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  // 检测iPad的多种方式
  const userAgent = navigator.userAgent;
  const isIPadUserAgent = /iPad/.test(userAgent);
  const isMacWithTouch = /Macintosh/.test(userAgent) && 'ontouchend' in document;
  
  // iPad Pro可能显示为Mac，需要额外检测
  const isIPadPro = isMacWithTouch && navigator.maxTouchPoints > 1;
  
  return isIPadUserAgent || isIPadPro;
};

/**
 * 阻止触摸滚动事件
 */
const preventTouchScroll = (e: TouchEvent) => {
  // 只阻止垂直滚动，保留水平滚动（如果需要）
  if (e.touches.length === 1) {
    e.preventDefault();
  }
};

/**
 * 阻止键盘滚动事件
 */
const preventKeyboardScroll = (e: KeyboardEvent) => {
  // 阻止空格键、方向键等导致的滚动
  const scrollKeys = ['Space', 'ArrowUp', 'ArrowDown', 'PageUp', 'PageDown', 'Home', 'End'];
  if (scrollKeys.includes(e.code)) {
    e.preventDefault();
  }
};

/**
 * 阻止鼠标滚轮事件（适用于iPad外接鼠标）
 */
const preventWheelScroll = (e: WheelEvent) => {
  e.preventDefault();
};

/**
 * iPad滚动控制Hook
 */
export const useScrollControl = (): ScrollControlConfig => {
  const [isIPad, setIsIPad] = useState(false);
  const [isScrollDisabled, setIsScrollDisabled] = useState(false);

  // 启用滚动控制
  const enableScrollControl = useCallback(() => {
    if (!isIPad) return;

    // 设置body样式
    document.body.style.overflow = 'hidden';
    document.body.style.touchAction = 'none';
    document.body.style.position = 'fixed';
    document.body.style.width = '100%';
    document.body.style.height = '100%';
    document.body.classList.add('ipad-no-scroll');

    // 添加事件监听器
    document.addEventListener('touchmove', preventTouchScroll, { passive: false });
    document.addEventListener('keydown', preventKeyboardScroll, { passive: false });
    document.addEventListener('wheel', preventWheelScroll, { passive: false });

    setIsScrollDisabled(true);

    console.log('🚫 iPad滚动控制已启用');
  }, [isIPad]);

  // 禁用滚动控制
  const disableScrollControl = useCallback(() => {
    // 恢复body样式
    document.body.style.overflow = '';
    document.body.style.touchAction = '';
    document.body.style.position = '';
    document.body.style.width = '';
    document.body.style.height = '';
    document.body.classList.remove('ipad-no-scroll');

    // 移除事件监听器
    document.removeEventListener('touchmove', preventTouchScroll);
    document.removeEventListener('keydown', preventKeyboardScroll);
    document.removeEventListener('wheel', preventWheelScroll);

    setIsScrollDisabled(false);

    console.log('✅ iPad滚动控制已禁用');
  }, []);

  // 初始化和清理
  useEffect(() => {
    // 检测设备类型
    const deviceIsIPad = detectIPad();
    setIsIPad(deviceIsIPad);

    if (deviceIsIPad) {
      console.log('📱 检测到iPad设备，准备启用滚动控制');
      
      // 延迟启用，确保页面完全加载
      const timer = setTimeout(() => {
        enableScrollControl();
      }, 100);

      return () => {
        clearTimeout(timer);
        disableScrollControl();
      };
    }

    return () => {
      disableScrollControl();
    };
  }, [enableScrollControl, disableScrollControl]);

  // 处理页面可见性变化
  useEffect(() => {
    if (!isIPad) return;

    const handleVisibilityChange = () => {
      if (document.hidden) {
        // 页面隐藏时禁用控制，避免影响其他应用
        disableScrollControl();
      } else {
        // 页面显示时重新启用控制
        enableScrollControl();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isIPad, enableScrollControl, disableScrollControl]);

  return {
    isIPad,
    isScrollDisabled,
    enableScrollControl,
    disableScrollControl,
  };
};
