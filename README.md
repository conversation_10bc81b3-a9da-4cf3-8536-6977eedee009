# Next.js & HeroUI 模板

这是一个使用 Next.js 14（app 目录）和 HeroUI (v2) 创建应用程序的模板。

[在 CodeSandbox 中试用](https://githubbox.com/heroui-inc/heroui/next-app-template)

## 使用的技术

- [Next.js 14](https://nextjs.org/docs/getting-started)
- [HeroUI v2](https://heroui.com/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Tailwind Variants](https://tailwind-variants.org)
- [TypeScript](https://www.typescriptlang.org/)
- [Framer Motion](https://www.framer.com/motion/)
- [next-themes](https://github.com/pacocoursey/next-themes)

## 如何使用

### 使用 create-next-app 创建项目

要基于此模板使用 `create-next-app` 创建新项目，请运行以下命令：

```bash
npx create-next-app -e https://github.com/heroui-inc/next-app-template
```

### 安装依赖

您可以使用 `npm`、`yarn`、`pnpm`、`bun` 中的任意一个，以下是使用 `npm` 的示例：

```bash
npm install
```

### 运行开发服务器

```bash
npm run dev
```

### 设置 pnpm（可选）

如果您使用 `pnpm`，需要在 `.npmrc` 文件中添加以下代码：

```bash
public-hoist-pattern[]=*@heroui/*
```

修改 `.npmrc` 文件后，您需要重新运行 `pnpm install` 以确保依赖项正确安装。

## 许可证

基于 [MIT 许可证](https://github.com/heroui-inc/next-app-template/blob/main/LICENSE) 授权。
