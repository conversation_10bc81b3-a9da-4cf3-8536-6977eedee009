/**
 * iPad移动端滚动控制样式
 * 专门解决iPad上的上下滚动问题
 */

/* iPad专用滚动控制 - 竖屏和横屏 */
@media (min-width: 768px) and (max-width: 1024px) {
  /* 主容器滚动控制 */
  .ipad-scroll-container {
    height: 100vh;
    height: 100dvh; /* 动态viewport高度，适配Safari地址栏变化 */
    overflow: hidden;
    touch-action: none; /* 禁用触摸滚动手势 */
    -webkit-overflow-scrolling: auto; /* 禁用iOS弹性滚动 */
    display: flex;
    flex-direction: column;
  }

  /* 内容区域高度控制 */
  .ipad-content-section {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 1rem 1.5rem; /* 减少iPad上的内边距 */
  }

  /* 确保motion元素在固定高度容器中正常工作 */
  .ipad-scroll-container .motion-element {
    height: 100%;
    overflow: hidden;
  }

  /* 卡片网格在iPad上的特殊处理 - 智能混合高度控制 */
  .ipad-content-section .grid {
    height: 100%;
    align-content: center;
    grid-template-rows: minmax(300px, 1fr); /* 最小300px，最大填满可用空间 */
    align-items: stretch; /* 确保grid项目拉伸到相同高度 */
  }

  /* 确保grid项目填满分配的空间 */
  .ipad-content-section .grid > * {
    height: 100%;
  }
}

/* iPad Pro横屏特殊处理 */
@media (min-width: 1024px) and (max-width: 1366px) and (orientation: landscape) {
  .ipad-scroll-container {
    height: 100vh;
    height: 100dvh;
    overflow: hidden;
    touch-action: none;
  }

  .ipad-content-section {
    padding: 0.75rem 1.5rem; /* iPad Pro横屏时进一步减少垂直内边距 */
  }
}

/* 防止在iPad上出现意外滚动的全局规则 */
@media (min-width: 768px) and (max-width: 1366px) {
  /* 禁用body在iPad上的滚动 */
  body.ipad-no-scroll {
    overflow: hidden;
    position: fixed;
    width: 100%;
    height: 100%;
  }

  /* 确保HeroUI组件在固定高度容器中正常工作 */
  .ipad-scroll-container [data-slot="base"] {
    height: 100%;
  }

  /* 修正Navbar在iPad上的垂直居中问题 - 更精确的选择器 */
  .ipad-scroll-container nav {
    display: flex !important;
    align-items: center !important;
    min-height: 64px !important;
  }

  /* HeroUI Navbar内部容器修正 */
  .ipad-scroll-container nav > div {
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
  }

  /* NavbarContent组件修正 */
  .ipad-scroll-container nav [data-slot="content"] {
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
  }

  /* Tabs容器垂直居中 */
  .ipad-scroll-container nav [role="tablist"] {
    display: flex !important;
    align-items: center !important;
  }

  /* 确保所有Navbar子元素垂直居中 */
  .ipad-scroll-container nav * {
    align-items: center !important;
  }

  /* 强制修正 - 如果上述选择器不够精确 */
  .ipad-scroll-container header,
  .ipad-scroll-container header > *,
  .ipad-scroll-container [data-slot="wrapper"],
  .ipad-scroll-container [class*="navbar"] {
    display: flex !important;
    align-items: center !important;
  }

  /* 动画过程中防止滚动 */
  .ipad-scroll-container .motion-element[data-framer-appear-id] {
    overflow: hidden;
  }
}

/* 高对比度模式下的iPad优化 */
@media (min-width: 768px) and (max-width: 1024px) and (prefers-contrast: high) {
  .ipad-scroll-container {
    border: 1px solid currentColor; /* 在高对比度模式下显示容器边界 */
  }
}

/* ========================================
 * 通用卡片等高样式 - 智能混合方案
 * 适用于桌面端和iPad设备
 * ======================================== */

/* 卡片等高容器样式 */
.card-equal-height {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 卡片头部固定高度，不参与拉伸 */
.card-equal-height [data-slot="header"] {
  flex-shrink: 0;
}

/* 卡片主体自适应拉伸，内容合理分布 */
.card-equal-height [data-slot="body"] {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 确保按钮等操作元素对齐到底部 */
.card-equal-height [data-slot="body"] > *:last-child {
  margin-top: auto;
}

/* ========================================
 * 桌面端卡片优化
 * 大屏幕下的智能高度适应
 * ======================================== */

/* 桌面端卡片网格优化 */
@media (min-width: 1025px) {
  .ipad-content-section .grid {
    grid-template-rows: minmax(640px, auto); /* 桌面端最小640px，允许内容驱动 */
    align-items: stretch;
  }

  .ipad-content-section .grid > * {
    height: 100%;
  }

  /* 桌面端卡片最大高度限制，避免过度拉伸 */
  .card-equal-height {
    max-height: 700px;
  }
}

/* 减少动画偏好下的iPad处理 */
@media (min-width: 768px) and (max-width: 1024px) and (prefers-reduced-motion: reduce) {
  .ipad-scroll-container .motion-element {
    transition: none !important;
    animation: none !important;
  }
}
