"use client";

import { useState, useCallback } from "react";
import { Link } from "@heroui/link";
import { AnimatePresence, motion } from "framer-motion";

import { Navbar } from "@/components/navbar";
import { HomeCardsLayout } from "@/components/home/<USER>";
import { HostCardsLayout } from "@/components/host/CardsLayout";
import { useTabAnimations } from "@/hooks/useTabAnimations";
import { createAnimationMonitor } from "@/utils/animationHelpers";
import { useScrollControl } from "@/hooks/useScrollControl";

export default function MainPage() {
  const [selectedTab, setSelectedTab] = useState("home");

  // 获取动画配置
  const { direction, variants } = useTabAnimations(selectedTab);

  // 创建动画监控器
  const animationMonitor = createAnimationMonitor();

  // iPad滚动控制 (JavaScript增强功能)
  const { isIPad, isScrollDisabled } = useScrollControl();

  // 开发模式下显示滚动控制状态
  if (process.env.NODE_ENV === 'development' && isIPad) {
    console.log(`📱 iPad检测: ${isIPad}, 滚动禁用: ${isScrollDisabled}`);
  }

  // 动画开始回调
  const onAnimationStart = useCallback(() => {
    animationMonitor.start(`tab-switch-to-${selectedTab}`);
  }, [selectedTab, animationMonitor]);

  // 动画完成回调
  const onAnimationComplete = useCallback(() => {
    animationMonitor.end(`tab-switch-to-${selectedTab}`);
  }, [selectedTab, animationMonitor]);

  const renderContent = () => {
    const ContentComponent = selectedTab === "home" ? HomeCardsLayout : HostCardsLayout;

    return (
      <AnimatePresence mode="popLayout" custom={direction}>
        <motion.div
          key={selectedTab}
          custom={direction}
          variants={variants}
          initial="enter"
          animate="center"
          exit="exit"
          onAnimationStart={onAnimationStart}
          onAnimationComplete={onAnimationComplete}
          className="w-full motion-element"
          style={{
            // 启用 GPU 加速
            willChange: "transform, opacity"
          }}
        >
          <ContentComponent />
        </motion.div>
      </AnimatePresence>
    );
  };

  return (
    <div className="relative flex flex-col h-screen ipad-scroll-container">
      <Navbar
        selectedTab={selectedTab}
        onTabChange={setSelectedTab}
      />
      <main className="container mx-auto max-w-7xl pt-0 px-0 flex-grow">
        {renderContent()}
      </main>
    </div>
  );
}
