{"name": "next-app-template", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint --fix"}, "dependencies": {"@heroui/button": "2.2.24", "@heroui/card": "^2.2.23", "@heroui/code": "2.2.18", "@heroui/input": "2.4.25", "@heroui/kbd": "2.2.19", "@heroui/link": "2.2.21", "@heroui/listbox": "2.3.23", "@heroui/navbar": "2.2.22", "@heroui/snippet": "2.2.25", "@heroui/switch": "2.2.22", "@heroui/system": "2.4.20", "@heroui/tabs": "^2.2.21", "@heroui/theme": "2.4.20", "@react-aria/ssr": "3.9.10", "@react-aria/visually-hidden": "3.8.26", "clsx": "2.1.1", "framer-motion": "11.18.2", "intl-messageformat": "10.7.16", "next": "15.3.1", "next-themes": "0.4.6", "react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"@next/eslint-plugin-next": "15.3.4", "@react-types/shared": "3.30.0", "@tailwindcss/postcss": "4.1.11", "@types/node": "20.5.7", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.34.1", "@typescript-eslint/parser": "8.34.1", "eslint": "9.25.1", "eslint-config-next": "15.3.4", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-unused-imports": "4.1.4", "globals": "16.0.0", "postcss": "8.5.6", "prettier": "3.5.3", "tailwind-variants": "2.0.1", "tailwindcss": "4.1.11", "typescript": "5.6.3"}}